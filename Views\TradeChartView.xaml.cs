using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Threading;
using BinanceOrderBookWPF.Models;
using BinanceOrderBookWPF.Services;
using ScottPlot;
using ScottPlot.WPF;

namespace BinanceOrderBookWPF.Views
{
    public partial class TradeChartView : UserControl
    {
        private OrderBookService? _orderBookService;
        private readonly List<TradePoint> _tradePoints = new();

        // 图表参数
        private DateTime _startTime = DateTime.UtcNow;
        private TimeSpan _timeWindow = TimeSpan.FromMinutes(30); // 可变时间窗口，默认30分钟
        private readonly TimeSpan _maxTimeWindow = TimeSpan.FromHours(4); // 最大4小时
        private readonly TimeSpan _minTimeWindow = TimeSpan.FromMinutes(1); // 最小1分钟

        // 防抖机制
        private readonly DispatcherTimer _redrawTimer;
        private bool _needsRedraw = false;

        // ScottPlot 散点图
        private ScottPlot.Plottables.Scatter? _buyScatter;
        private ScottPlot.Plottables.Scatter? _sellScatter;

        // 颜色定义
        private readonly ScottPlot.Color _buyColor = ScottPlot.Color.FromHex("#0ECB81");
        private readonly ScottPlot.Color _sellColor = ScottPlot.Color.FromHex("#F84960");
        private readonly ScottPlot.Color _backgroundColor = ScottPlot.Color.FromHex("#1E1E1E");
        private readonly ScottPlot.Color _gridColor = ScottPlot.Color.FromHex("#2B3139");
        private readonly ScottPlot.Color _textColor = ScottPlot.Color.FromHex("#848E9C");

        public TradeChartView()
        {
            System.Diagnostics.Debug.WriteLine("TradeChartView: 构造函数开始");
            InitializeComponent();
            Loaded += TradeChartView_Loaded;
            DataContextChanged += TradeChartView_DataContextChanged;

            // 初始化防抖定时器
            _redrawTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(100) // 100ms防抖延迟，提供实时显示效果
            };
            _redrawTimer.Tick += RedrawTimer_Tick;

            System.Diagnostics.Debug.WriteLine("TradeChartView: 构造函数完成");
        }

        private void TradeChartView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"TradeChartView_DataContextChanged: 旧值={e.OldValue?.GetType()?.Name}, 新值={e.NewValue?.GetType()?.Name}");

            if (e.NewValue is OrderBookService orderBookService)
            {
                System.Diagnostics.Debug.WriteLine($"TradeChartView_DataContextChanged: 获得OrderBookService，交易数量={orderBookService.RecentTrades?.Count ?? 0}");

                // 如果已经加载完成，立即处理
                if (IsLoaded)
                {
                    System.Diagnostics.Debug.WriteLine("TradeChartView_DataContextChanged: 控件已加载，立即处理DataContext");
                    HandleDataContextChange(orderBookService);
                }
            }
        }

        private void HandleDataContextChange(OrderBookService orderBookService)
        {
            System.Diagnostics.Debug.WriteLine("TradeChartView.HandleDataContextChange: 开始处理");
            Console.WriteLine("TradeChartView.HandleDataContextChange: 开始处理");

            // 取消之前的订阅
            if (_orderBookService != null)
            {
                _orderBookService.RecentTrades.CollectionChanged -= RecentTrades_CollectionChanged;
                System.Diagnostics.Debug.WriteLine("TradeChartView.HandleDataContextChange: 取消之前的订阅");
                Console.WriteLine("TradeChartView.HandleDataContextChange: 取消之前的订阅");
            }

            _orderBookService = orderBookService;
            _orderBookService.RecentTrades.CollectionChanged += RecentTrades_CollectionChanged;

            System.Diagnostics.Debug.WriteLine($"TradeChartView.HandleDataContextChange: 找到OrderBookService，现有交易数量: {_orderBookService.RecentTrades?.Count ?? 0}");
            Console.WriteLine($"TradeChartView.HandleDataContextChange: 找到OrderBookService，现有交易数量: {_orderBookService.RecentTrades?.Count ?? 0}");

            // 加载现有的交易数据
            LoadExistingTrades();

            // 更新图表
            UpdateChart();

            System.Diagnostics.Debug.WriteLine("TradeChartView.HandleDataContextChange: 处理完成");
            Console.WriteLine("TradeChartView.HandleDataContextChange: 处理完成");
        }

        private void TradeChartView_Loaded(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("TradeChartView_Loaded: 开始加载");

            // 初始化 ScottPlot
            InitializeChart();

            // 初始化时间起点
            _startTime = DateTime.UtcNow;

            // 初始化时间窗口显示
            UpdateTimeWindowDisplay();

            if (DataContext is OrderBookService orderBookService)
            {
                System.Diagnostics.Debug.WriteLine("TradeChartView_Loaded: DataContext是OrderBookService，处理数据");
                HandleDataContextChange(orderBookService);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"TradeChartView_Loaded: DataContext不是OrderBookService，类型={DataContext?.GetType()?.Name ?? "null"}，添加测试数据");
                // 添加一些测试数据
                AddTestData();
                // 更新图表
                UpdateChart();
            }
        }



        private void LoadExistingTrades()
        {
            if (_orderBookService?.RecentTrades != null)
            {
                System.Diagnostics.Debug.WriteLine($"LoadExistingTrades: 开始加载 {_orderBookService.RecentTrades.Count} 个现有交易");
                foreach (var trade in _orderBookService.RecentTrades)
                {
                    AddTradePoint(trade);
                }
                System.Diagnostics.Debug.WriteLine($"LoadExistingTrades: 完成，总交易点数: {_tradePoints.Count}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("LoadExistingTrades: 没有找到交易数据");
            }
        }

        private void AddTestData()
        {
            System.Diagnostics.Debug.WriteLine("AddTestData: 添加测试数据");
            var basePrice = 117600.0;
            var now = DateTime.UtcNow;

            // 添加一些测试交易点
            for (int i = 0; i < 20; i++)
            {
                var tradePoint = new TradePoint
                {
                    Price = basePrice + (i % 2 == 0 ? 1 : -1) * (i * 0.5),
                    Quantity = 0.1 + (i * 0.05),
                    Time = now.AddSeconds(-60 + i * 3),
                    IsBuyerMaker = i % 2 == 0,
                    Amount = (basePrice + (i % 2 == 0 ? 1 : -1) * (i * 0.5)) * (0.1 + (i * 0.05))
                };

                _tradePoints.Add(tradePoint);
            }

            System.Diagnostics.Debug.WriteLine($"AddTestData: 完成，添加了 {_tradePoints.Count} 个测试交易点");
        }

        private void TradeChartView_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            RequestRedraw();
        }

        private void InitializeChart()
        {
            System.Diagnostics.Debug.WriteLine("InitializeChart: 初始化 ScottPlot 图表");

            // 设置图表背景颜色
            TradeChart.Plot.FigureBackground.Color = _backgroundColor;
            TradeChart.Plot.DataBackground.Color = _backgroundColor;

            // 设置网格样式
            TradeChart.Plot.Grid.MajorLineColor = _gridColor;

            // 设置坐标轴样式
            TradeChart.Plot.Axes.Bottom.Label.Text = "时间";
            TradeChart.Plot.Axes.Left.Label.Text = "价格";

            // 初始化散点图
            _buyScatter = TradeChart.Plot.Add.Scatter(new double[0], new double[0]);
            _buyScatter.Color = _buyColor;
            _buyScatter.MarkerSize = 8;
            _buyScatter.MarkerShape = ScottPlot.MarkerShape.FilledCircle;
            _buyScatter.LineWidth = 0;
            _buyScatter.LegendText = "买入";

            _sellScatter = TradeChart.Plot.Add.Scatter(new double[0], new double[0]);
            _sellScatter.Color = _sellColor;
            _sellScatter.MarkerSize = 8;
            _sellScatter.MarkerShape = ScottPlot.MarkerShape.FilledCircle;
            _sellScatter.LineWidth = 0;
            _sellScatter.LegendText = "卖出";

            // 显示图例
            TradeChart.Plot.ShowLegend();

            System.Diagnostics.Debug.WriteLine("InitializeChart: 完成");
        }

        private void RedrawTimer_Tick(object? sender, EventArgs e)
        {
            _redrawTimer.Stop();
            if (_needsRedraw)
            {
                _needsRedraw = false;
                UpdateChart();
            }
        }

        private void RequestRedraw()
        {
            _needsRedraw = true;
            _redrawTimer.Stop();
            _redrawTimer.Start();
        }

        private void RecentTrades_CollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            // 减少调试输出的频率，避免控制台输出过多
            if (e.Action == NotifyCollectionChangedAction.Add && e.NewItems != null)
            {
                foreach (TradeRecord trade in e.NewItems)
                {
                    AddTradePoint(trade);
                }
            }

            // 清理过期的交易点
            CleanupOldTrades();

            // 使用防抖机制请求重绘，避免频繁更新UI
            RequestRedraw();
        }

        private void AddTradePoint(TradeRecord trade)
        {
            var tradePoint = new TradePoint
            {
                Price = trade.Price,
                Quantity = trade.Quantity,
                Time = trade.Time,
                IsBuyerMaker = trade.IsBuyerMaker,
                Amount = trade.Price * trade.Quantity
            };

            _tradePoints.Add(tradePoint);
        }

        private void CleanupOldTrades()
        {
            // 使用UTC时间，因为交易记录的时间戳是UTC时间
            // 使用最大时间窗口来保留数据，这样缩放时不会丢失数据
            var cutoffTime = DateTime.UtcNow - _maxTimeWindow;
            var beforeCount = _tradePoints.Count;

            _tradePoints.RemoveAll(tp => tp.Time < cutoffTime);
            var afterCount = _tradePoints.Count;

            // 只在有实际清理时才输出调试信息
            if (beforeCount != afterCount)
            {
                System.Diagnostics.Debug.WriteLine($"TradeChartView.CleanupOldTrades: 清理完成，删除了{beforeCount - afterCount}个交易点，剩余{afterCount}个");
            }
        }

        private void UpdateChart()
        {
            System.Diagnostics.Debug.WriteLine($"UpdateChart: 开始更新图表，交易点数量: {_tradePoints.Count}");

            if (_tradePoints.Count == 0)
            {
                // 清空图表
                TradeChart.Plot.Clear();
                TradeChart.Refresh();
                return;
            }

            // 获取当前时间窗口内的可见交易点
            var now = DateTime.UtcNow;
            var visibleTrades = _tradePoints.Where(tp => (now - tp.Time) <= _timeWindow).ToList();

            if (!visibleTrades.Any())
            {
                // 清空图表
                TradeChart.Plot.Clear();
                TradeChart.Refresh();
                return;
            }

            // 分离买入和卖出交易
            var buyTrades = visibleTrades.Where(tp => !tp.IsBuyerMaker).ToList(); // IsBuyerMaker为false表示买入
            var sellTrades = visibleTrades.Where(tp => tp.IsBuyerMaker).ToList(); // IsBuyerMaker为true表示卖出

            // 清空现有的散点图
            TradeChart.Plot.Clear();

            // 添加买入数据
            if (buyTrades.Any())
            {
                var buyX = buyTrades.Select(tp => tp.Time.ToOADate()).ToArray();
                var buyY = buyTrades.Select(tp => tp.Price).ToArray();

                _buyScatter = TradeChart.Plot.Add.Scatter(buyX, buyY);
                _buyScatter.Color = _buyColor;
                _buyScatter.MarkerSize = 8;
                _buyScatter.MarkerShape = ScottPlot.MarkerShape.FilledCircle;
                _buyScatter.LineWidth = 0;
                _buyScatter.LegendText = "买入";
            }

            // 添加卖出数据
            if (sellTrades.Any())
            {
                var sellX = sellTrades.Select(tp => tp.Time.ToOADate()).ToArray();
                var sellY = sellTrades.Select(tp => tp.Price).ToArray();

                _sellScatter = TradeChart.Plot.Add.Scatter(sellX, sellY);
                _sellScatter.Color = _sellColor;
                _sellScatter.MarkerSize = 8;
                _sellScatter.MarkerShape = ScottPlot.MarkerShape.FilledCircle;
                _sellScatter.LineWidth = 0;
                _sellScatter.LegendText = "卖出";
            }

            // 设置坐标轴范围
            if (visibleTrades.Any())
            {
                var minTime = visibleTrades.Min(tp => tp.Time.ToOADate());
                var maxTime = visibleTrades.Max(tp => tp.Time.ToOADate());
                var minPrice = visibleTrades.Min(tp => tp.Price);
                var maxPrice = visibleTrades.Max(tp => tp.Price);

                // 添加一些边距
                var timeRange = maxTime - minTime;
                var priceRange = maxPrice - minPrice;

                if (timeRange > 0 && priceRange > 0)
                {
                    TradeChart.Plot.Axes.SetLimits(
                        minTime - timeRange * 0.05,
                        maxTime + timeRange * 0.05,
                        minPrice - priceRange * 0.1,
                        maxPrice + priceRange * 0.1
                    );
                }
            }

            // 显示图例
            TradeChart.Plot.ShowLegend();

            // 刷新图表
            TradeChart.Refresh();

            System.Diagnostics.Debug.WriteLine($"UpdateChart: 完成，买入点: {buyTrades.Count}, 卖出点: {sellTrades.Count}");
        }

        private void UpdateTimeWindowDisplay()
        {
            if (TimeWindowDisplay != null)
            {
                var totalMinutes = _timeWindow.TotalMinutes;
                string displayText;

                if (totalMinutes < 60)
                {
                    displayText = $"{totalMinutes:F0}分钟";
                }
                else
                {
                    var hours = totalMinutes / 60;
                    displayText = $"{hours:F1}小时";
                }

                TimeWindowDisplay.Text = displayText;
            }
        }
    }

    // 交易点数据结构
    public class TradePoint
    {
        public double Price { get; set; }
        public double Quantity { get; set; }
        public DateTime Time { get; set; }
        public bool IsBuyerMaker { get; set; }
        public double Amount { get; set; }
    }
}
